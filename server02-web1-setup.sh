#!/bin/bash

# Server02 - WEB服务器1配置脚本
# 系统: CentOS 9
# 功能: Apache + MariaDB + PHP，端口80
# 包含错误检测和自动修复功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 错误处理函数
handle_error() {
    log_error "脚本执行失败，行号: $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 获取网络配置信息
get_network_config() {
    log_step "获取网络配置信息..."

    # 获取当前IP地址
    CURRENT_IP=$(ip route get ******* | awk '{print $7; exit}')

    # 获取网络接口名称
    INTERFACE=$(ip route | grep default | awk '{print $5}' | head -1)

    # 获取网关
    GATEWAY=$(ip route | grep default | awk '{print $3}' | head -1)

    log_info "当前IP地址: $CURRENT_IP"
    log_info "网络接口: $INTERFACE"
    log_info "网关: $GATEWAY"

    # 用户输入配置信息
    read -p "请输入网站域名 (例如: www.example.com): " WEBSITE_DOMAIN
    read -p "请输入MySQL root密码: " -s MYSQL_ROOT_PASSWORD
    echo
    read -p "请输入网站数据库名称: " DB_NAME
    read -p "请输入网站数据库用户名: " DB_USER
    read -p "请输入网站数据库密码: " -s DB_PASSWORD
    echo

    # DNS服务器配置
    echo
    echo "DNS服务器配置 (可选，用于域名解析):"
    read -p "请输入DNS服务器IP地址 (按回车跳过): " DNS_SERVER_IP
    if [[ -n "$DNS_SERVER_IP" ]]; then
        read -p "请输入主域名 (例如: example.com): " MAIN_DOMAIN
        USE_CUSTOM_DNS=true
    else
        USE_CUSTOM_DNS=false
    fi

    # 验证输入
    if [[ -z "$WEBSITE_DOMAIN" || -z "$MYSQL_ROOT_PASSWORD" || -z "$DB_NAME" || -z "$DB_USER" || -z "$DB_PASSWORD" ]]; then
        log_error "所有配置信息都不能为空"
        exit 1
    fi
}

# 系统更新
update_system() {
    log_step "更新系统..."
    dnf update -y
    dnf install -y epel-release
}

# 安装Apache
install_apache() {
    log_step "安装Apache HTTP服务器..."
    dnf install -y httpd httpd-tools

    # 启动并启用Apache
    systemctl start httpd
    systemctl enable httpd

    if systemctl is-active --quiet httpd; then
        log_info "Apache安装并启动成功"
    else
        log_error "Apache启动失败"
        return 1
    fi
}

# 安装PHP
install_php() {
    log_step "安装PHP及相关模块..."
    dnf install -y php php-cli php-fpm php-mysqlnd php-zip php-devel php-gd php-mcrypt php-mbstring php-curl php-xml php-pear php-bcmath php-json php-opcache

    # 启动并启用PHP-FPM
    systemctl start php-fpm
    systemctl enable php-fpm

    if systemctl is-active --quiet php-fpm; then
        log_info "PHP安装并启动成功"
    else
        log_error "PHP-FPM启动失败"
        return 1
    fi
}

# 安装MariaDB
install_mariadb() {
    log_step "安装MariaDB数据库..."
    dnf install -y mariadb-server mariadb

    # 启动并启用MariaDB
    systemctl start mariadb
    systemctl enable mariadb

    if systemctl is-active --quiet mariadb; then
        log_info "MariaDB安装并启动成功"
    else
        log_error "MariaDB启动失败"
        return 1
    fi
}

# 配置MariaDB安全设置
secure_mariadb() {
    log_step "配置MariaDB安全设置..."

    # 设置root密码
    mysql -e "UPDATE mysql.user SET Password = PASSWORD('$MYSQL_ROOT_PASSWORD') WHERE User = 'root'"
    mysql -e "DELETE FROM mysql.user WHERE User=''"
    mysql -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1')"
    mysql -e "DROP DATABASE IF EXISTS test"
    mysql -e "DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%'"
    mysql -e "FLUSH PRIVILEGES"

    log_info "MariaDB安全配置完成"
}

# 创建网站数据库和用户
create_database() {
    log_step "创建网站数据库和用户..."

    mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';
FLUSH PRIVILEGES;
EOF

    log_info "数据库 $DB_NAME 和用户 $DB_USER 创建成功"
}

# 配置Apache虚拟主机
configure_apache_vhost() {
    log_step "配置Apache虚拟主机..."

    # 创建网站目录
    mkdir -p /var/www/$WEBSITE_DOMAIN

    # 创建虚拟主机配置文件
    cat > /etc/httpd/conf.d/$WEBSITE_DOMAIN.conf << EOF
<VirtualHost *:80>
    ServerName $WEBSITE_DOMAIN
    DocumentRoot /var/www/$WEBSITE_DOMAIN

    <Directory /var/www/$WEBSITE_DOMAIN>
        AllowOverride All
        Require all granted
    </Directory>

    ErrorLog /var/log/httpd/${WEBSITE_DOMAIN}_error.log
    CustomLog /var/log/httpd/${WEBSITE_DOMAIN}_access.log combined
</VirtualHost>
EOF

    # 设置目录权限
    chown -R apache:apache /var/www/$WEBSITE_DOMAIN
    chmod -R 755 /var/www/$WEBSITE_DOMAIN

    log_info "Apache虚拟主机配置完成"
}

# 创建测试页面
create_test_pages() {
    log_step "创建测试页面..."

    # 创建HTML测试页面
    cat > /var/www/$WEBSITE_DOMAIN/index.html << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$WEBSITE_DOMAIN - 测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f4f4f4; }
        .container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .success { color: #28a745; }
        .links { margin-top: 20px; }
        .links a { display: inline-block; margin: 5px 10px 5px 0; padding: 8px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        .links a:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="success">🎉 Web服务器配置成功！</h1>
        <div class="info">
            <h3>服务器信息</h3>
            <p><strong>域名:</strong> $WEBSITE_DOMAIN</p>
            <p><strong>服务器IP:</strong> $CURRENT_IP</p>
            <p><strong>服务:</strong> Apache + MariaDB + PHP</p>
            <p><strong>端口:</strong> 80</p>
        </div>
        <div class="links">
            <a href="phpinfo.php">PHP信息</a>
            <a href="dbtest.php">数据库测试</a>
        </div>
    </div>
</body>
</html>
EOF

    # 创建PHP信息页面
    cat > /var/www/$WEBSITE_DOMAIN/phpinfo.php << EOF
<?php
phpinfo();
?>
EOF

    # 创建数据库连接测试页面
    cat > /var/www/$WEBSITE_DOMAIN/dbtest.php << EOF
<?php
\$servername = "localhost";
\$username = "$DB_USER";
\$password = "$DB_PASSWORD";
\$dbname = "$DB_NAME";

try {
    \$pdo = new PDO("mysql:host=\$servername;dbname=\$dbname", \$username, \$password);
    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<h2 style='color: green;'>数据库连接成功！</h2>";
    echo "<p>数据库名称: \$dbname</p>";
    echo "<p>连接时间: " . date('Y-m-d H:i:s') . "</p>";
} catch(PDOException \$e) {
    echo "<h2 style='color: red;'>数据库连接失败：</h2>";
    echo "<p>" . \$e->getMessage() . "</p>";
}
?>
EOF

    # 设置文件权限
    chown -R apache:apache /var/www/$WEBSITE_DOMAIN
    chmod 644 /var/www/$WEBSITE_DOMAIN/*.html
    chmod 644 /var/www/$WEBSITE_DOMAIN/*.php

    log_info "测试页面创建完成"
}

# 配置PHP
configure_php() {
    log_step "配置PHP..."

    # 备份原配置文件
    cp /etc/php.ini /etc/php.ini.backup

    # 修改PHP配置
    sed -i 's/;date.timezone =/date.timezone = Asia\/Shanghai/' /etc/php.ini
    sed -i 's/upload_max_filesize = 2M/upload_max_filesize = 64M/' /etc/php.ini
    sed -i 's/post_max_size = 8M/post_max_size = 64M/' /etc/php.ini
    sed -i 's/max_execution_time = 30/max_execution_time = 300/' /etc/php.ini
    sed -i 's/memory_limit = 128M/memory_limit = 256M/' /etc/php.ini

    log_info "PHP配置完成"
}

# 配置DNS客户端
configure_dns_client() {
    if [[ "$USE_CUSTOM_DNS" == "true" ]]; then
        log_step "配置DNS客户端..."

        # 备份原始resolv.conf
        cp /etc/resolv.conf /etc/resolv.conf.backup

        # 配置自定义DNS服务器
        cat > /etc/resolv.conf << EOF
# 自定义DNS配置
nameserver $DNS_SERVER_IP
nameserver *******
nameserver *******

search $MAIN_DOMAIN
domain $MAIN_DOMAIN
EOF

        # 防止NetworkManager覆盖resolv.conf
        chattr +i /etc/resolv.conf

        log_info "DNS客户端配置完成，使用DNS服务器: $DNS_SERVER_IP"

        # 测试DNS解析
        if nslookup $MAIN_DOMAIN $DNS_SERVER_IP > /dev/null 2>&1; then
            log_info "DNS解析测试成功"
        else
            log_warn "DNS解析测试失败，请检查DNS服务器配置"
        fi
    else
        log_info "跳过DNS客户端配置"
    fi
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."

    systemctl start firewalld
    systemctl enable firewalld

    # 开放HTTP端口
    firewall-cmd --permanent --add-service=http
    firewall-cmd --permanent --add-port=80/tcp
    firewall-cmd --reload

    log_info "防火墙配置完成，已开放HTTP服务端口80"
}

# 重启服务
restart_services() {
    log_step "重启所有服务..."

    systemctl restart httpd
    systemctl restart php-fpm
    systemctl restart mariadb

    # 检查服务状态
    if systemctl is-active --quiet httpd && systemctl is-active --quiet php-fpm && systemctl is-active --quiet mariadb; then
        log_info "所有服务重启成功"
    else
        log_error "部分服务重启失败"
        return 1
    fi
}

# 测试网站
test_website() {
    log_step "测试网站..."

    # 等待服务完全启动
    sleep 5

    # 测试HTTP连接
    if curl -s -o /dev/null -w "%{http_code}" http://$CURRENT_IP | grep -q "200"; then
        log_info "HTTP连接测试成功"
    else
        log_warn "HTTP连接测试失败"
    fi

    # 测试PHP
    if curl -s http://$CURRENT_IP/phpinfo.php | grep -q "PHP Version"; then
        log_info "PHP测试成功"
    else
        log_warn "PHP测试失败"
    fi
}

# 显示配置信息
show_config_info() {
    log_step "Web服务器1配置完成！"
    echo
    echo "=================================="
    echo "Web服务器1配置信息:"
    echo "=================================="
    echo "服务器IP: $CURRENT_IP"
    echo "网站域名: $WEBSITE_DOMAIN"
    echo "HTTP端口: 80"
    echo "网络接口: $INTERFACE"
    echo "网关: $GATEWAY"
    echo
    echo "数据库信息:"
    echo "数据库名称: $DB_NAME"
    echo "数据库用户: $DB_USER"
    echo "MySQL Root密码: [已设置]"
    echo
    echo "访问地址:"
    echo "http://$CURRENT_IP"
    echo "http://$WEBSITE_DOMAIN (需要DNS解析)"
    echo
    echo "测试页面:"
    echo "http://$CURRENT_IP/phpinfo.php"
    echo "http://$CURRENT_IP/dbtest.php"
    echo
    echo "重要目录:"
    echo "网站根目录: /var/www/$WEBSITE_DOMAIN"
    echo "Apache配置: /etc/httpd/conf.d/$WEBSITE_DOMAIN.conf"
    echo "PHP配置: /etc/php.ini"
    echo "=================================="
}

# 主函数
main() {
    log_info "开始配置Web服务器1..."

    check_root
    get_network_config
    update_system
    configure_dns_client
    install_apache
    install_php
    install_mariadb
    secure_mariadb
    create_database
    configure_apache_vhost
    configure_php
    create_test_pages
    configure_firewall
    restart_services
    test_website
    show_config_info

    log_info "Web服务器1配置完成！"
}

# 执行主函数
main "$@"
