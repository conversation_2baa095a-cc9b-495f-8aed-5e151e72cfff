#!/bin/bash

# Server03 - WEB服务器2配置脚本
# 系统: CentOS 9
# 功能: Apache + SSL，端口443
# 包含错误检测和自动修复功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 错误处理函数
handle_error() {
    log_error "脚本执行失败，行号: $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 获取网络配置信息
get_network_config() {
    log_step "获取网络配置信息..."
    
    # 获取当前IP地址
    CURRENT_IP=$(ip route get ******* | awk '{print $7; exit}')
    
    # 获取网络接口名称
    INTERFACE=$(ip route | grep default | awk '{print $5}' | head -1)
    
    # 获取网关
    GATEWAY=$(ip route | grep default | awk '{print $3}' | head -1)
    
    log_info "当前IP地址: $CURRENT_IP"
    log_info "网络接口: $INTERFACE"
    log_info "网关: $GATEWAY"
    
    # 用户输入配置信息
    read -p "请输入SSL网站域名 (例如: secure.example.com): " SSL_DOMAIN
    read -p "请输入组织名称 (例如: My Company): " ORG_NAME
    read -p "请输入国家代码 (例如: CN): " COUNTRY_CODE
    read -p "请输入省份 (例如: Beijing): " STATE
    read -p "请输入城市 (例如: Beijing): " CITY
    
    # 验证输入
    if [[ -z "$SSL_DOMAIN" || -z "$ORG_NAME" || -z "$COUNTRY_CODE" || -z "$STATE" || -z "$CITY" ]]; then
        log_error "所有配置信息都不能为空"
        exit 1
    fi
    
    # 设置证书相关变量
    CERT_DIR="/etc/ssl/certs"
    PRIVATE_KEY_DIR="/etc/ssl/private"
    CERT_FILE="$CERT_DIR/$SSL_DOMAIN.crt"
    KEY_FILE="$PRIVATE_KEY_DIR/$SSL_DOMAIN.key"
    CSR_FILE="/tmp/$SSL_DOMAIN.csr"
}

# 系统更新
update_system() {
    log_step "更新系统..."
    dnf update -y
    dnf install -y epel-release
}

# 安装Apache和SSL模块
install_apache_ssl() {
    log_step "安装Apache HTTP服务器和SSL模块..."
    dnf install -y httpd httpd-tools mod_ssl openssl
    
    # 启动并启用Apache
    systemctl start httpd
    systemctl enable httpd
    
    if systemctl is-active --quiet httpd; then
        log_info "Apache安装并启动成功"
    else
        log_error "Apache启动失败"
        return 1
    fi
}

# 创建SSL证书目录
create_ssl_dirs() {
    log_step "创建SSL证书目录..."
    
    mkdir -p $CERT_DIR
    mkdir -p $PRIVATE_KEY_DIR
    
    # 设置目录权限
    chmod 755 $CERT_DIR
    chmod 700 $PRIVATE_KEY_DIR
    
    log_info "SSL证书目录创建完成"
}

# 生成自签名SSL证书
generate_ssl_certificate() {
    log_step "生成自签名SSL证书..."
    
    # 生成私钥
    openssl genrsa -out $KEY_FILE 2048
    
    # 生成证书签名请求
    openssl req -new -key $KEY_FILE -out $CSR_FILE -subj "/C=$COUNTRY_CODE/ST=$STATE/L=$CITY/O=$ORG_NAME/CN=$SSL_DOMAIN"
    
    # 生成自签名证书（有效期1年）
    openssl x509 -req -days 365 -in $CSR_FILE -signkey $KEY_FILE -out $CERT_FILE
    
    # 设置证书文件权限
    chmod 644 $CERT_FILE
    chmod 600 $KEY_FILE
    
    # 清理临时文件
    rm -f $CSR_FILE
    
    log_info "SSL证书生成完成"
    log_info "证书文件: $CERT_FILE"
    log_info "私钥文件: $KEY_FILE"
}

# 配置Apache SSL虚拟主机
configure_ssl_vhost() {
    log_step "配置Apache SSL虚拟主机..."
    
    # 创建网站目录
    mkdir -p /var/www/$SSL_DOMAIN
    
    # 创建SSL虚拟主机配置文件
    cat > /etc/httpd/conf.d/$SSL_DOMAIN-ssl.conf << EOF
<IfModule mod_ssl.c>
    <VirtualHost *:443>
        ServerName $SSL_DOMAIN
        DocumentRoot /var/www/$SSL_DOMAIN
        
        # SSL配置
        SSLEngine on
        SSLCertificateFile $CERT_FILE
        SSLCertificateKeyFile $KEY_FILE
        
        # SSL协议配置
        SSLProtocol all -SSLv2 -SSLv3
        SSLCipherSuite ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256
        SSLHonorCipherOrder on
        SSLCompression off
        
        # HSTS (可选)
        Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
        Header always set X-Frame-Options DENY
        Header always set X-Content-Type-Options nosniff
        
        <Directory /var/www/$SSL_DOMAIN>
            AllowOverride All
            Require all granted
        </Directory>
        
        ErrorLog /var/log/httpd/${SSL_DOMAIN}_ssl_error.log
        CustomLog /var/log/httpd/${SSL_DOMAIN}_ssl_access.log combined
    </VirtualHost>
</IfModule>

# HTTP到HTTPS重定向
<VirtualHost *:80>
    ServerName $SSL_DOMAIN
    Redirect permanent / https://$SSL_DOMAIN/
</VirtualHost>
EOF
    
    # 设置目录权限
    chown -R apache:apache /var/www/$SSL_DOMAIN
    chmod -R 755 /var/www/$SSL_DOMAIN
    
    log_info "Apache SSL虚拟主机配置完成"
}

# 创建SSL测试页面
create_ssl_test_pages() {
    log_step "创建SSL测试页面..."
    
    # 创建HTML测试页面
    cat > /var/www/$SSL_DOMAIN/index.html << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$SSL_DOMAIN - SSL安全网站</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .container { background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; box-shadow: 0 8px 32px rgba(0,0,0,0.3); backdrop-filter: blur(10px); }
        h1 { color: #fff; text-align: center; margin-bottom: 30px; }
        .ssl-info { background: rgba(255,255,255,0.2); padding: 20px; border-radius: 10px; margin: 20px 0; }
        .success { color: #00ff88; font-weight: bold; }
        .cert-info { background: rgba(0,0,0,0.2); padding: 15px; border-radius: 8px; margin: 15px 0; font-family: monospace; }
        .security-features { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
        .feature { background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; text-align: center; }
        .feature h3 { margin: 0 0 10px 0; color: #00ff88; }
        .lock-icon { font-size: 2em; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 SSL安全网站配置成功！</h1>
        
        <div class="ssl-info">
            <h2 class="success">✅ HTTPS连接已启用</h2>
            <div class="cert-info">
                <strong>域名:</strong> $SSL_DOMAIN<br>
                <strong>服务器IP:</strong> $CURRENT_IP<br>
                <strong>SSL端口:</strong> 443<br>
                <strong>证书类型:</strong> 自签名证书<br>
                <strong>加密强度:</strong> 2048位 RSA
            </div>
        </div>
        
        <div class="security-features">
            <div class="feature">
                <div class="lock-icon">🔐</div>
                <h3>SSL/TLS加密</h3>
                <p>所有数据传输都经过加密保护</p>
            </div>
            <div class="feature">
                <div class="lock-icon">🛡️</div>
                <h3>HSTS启用</h3>
                <p>强制使用HTTPS连接</p>
            </div>
            <div class="feature">
                <div class="lock-icon">🔒</div>
                <h3>安全头部</h3>
                <p>X-Frame-Options, X-Content-Type-Options</p>
            </div>
            <div class="feature">
                <div class="lock-icon">⚡</div>
                <h3>HTTP重定向</h3>
                <p>自动从HTTP重定向到HTTPS</p>
            </div>
        </div>
        
        <div class="ssl-info">
            <h3>证书信息</h3>
            <p><strong>颁发给:</strong> $SSL_DOMAIN</p>
            <p><strong>颁发者:</strong> $ORG_NAME</p>
            <p><strong>国家:</strong> $COUNTRY_CODE</p>
            <p><strong>省份:</strong> $STATE</p>
            <p><strong>城市:</strong> $CITY</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <p><strong>注意:</strong> 这是自签名证书，浏览器可能会显示安全警告。</p>
            <p>在生产环境中，请使用由受信任的CA颁发的证书。</p>
        </div>
    </div>
    
    <script>
        // 显示连接信息
        if (location.protocol === 'https:') {
            console.log('✅ 安全的HTTPS连接已建立');
        }
        
        // 检查SSL状态
        document.addEventListener('DOMContentLoaded', function() {
            const protocol = location.protocol;
            const isSecure = protocol === 'https:';
            
            if (isSecure) {
                document.title = '🔒 ' + document.title;
            }
        });
    </script>
</body>
</html>
EOF
    
    # 创建SSL状态检查页面
    cat > /var/www/$SSL_DOMAIN/ssl-status.html << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL状态检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .status-container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-item { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .status-ok { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="status-container">
        <h1>SSL状态检查</h1>
        <div id="ssl-status"></div>
    </div>
    
    <script>
        function checkSSLStatus() {
            const statusDiv = document.getElementById('ssl-status');
            const protocol = location.protocol;
            const hostname = location.hostname;
            const port = location.port || (protocol === 'https:' ? '443' : '80');
            
            let statusHTML = '';
            
            // 检查协议
            if (protocol === 'https:') {
                statusHTML += '<div class="status-item status-ok">✅ 使用HTTPS协议</div>';
            } else {
                statusHTML += '<div class="status-item status-error">❌ 未使用HTTPS协议</div>';
            }
            
            // 检查端口
            if (port === '443') {
                statusHTML += '<div class="status-item status-ok">✅ 使用标准SSL端口 (443)</div>';
            } else {
                statusHTML += '<div class="status-item status-warning">⚠️ 非标准SSL端口: ' + port + '</div>';
            }
            
            // 显示连接信息
            statusHTML += '<div class="status-item status-ok">📍 服务器: ' + hostname + '</div>';
            statusHTML += '<div class="status-item status-ok">🔗 完整URL: ' + location.href + '</div>';
            
            // 检查安全头部
            statusHTML += '<div class="status-item status-ok">🛡️ 安全头部已配置</div>';
            
            statusDiv.innerHTML = statusHTML;
        }
        
        document.addEventListener('DOMContentLoaded', checkSSLStatus);
    </script>
</body>
</html>
EOF
    
    # 设置文件权限
    chown -R apache:apache /var/www/$SSL_DOMAIN
    chmod 644 /var/www/$SSL_DOMAIN/*.html
    
    log_info "SSL测试页面创建完成"
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    systemctl start firewalld
    systemctl enable firewalld
    
    # 开放HTTPS端口
    firewall-cmd --permanent --add-service=https
    firewall-cmd --permanent --add-port=443/tcp
    # 也开放HTTP端口用于重定向
    firewall-cmd --permanent --add-service=http
    firewall-cmd --permanent --add-port=80/tcp
    firewall-cmd --reload
    
    log_info "防火墙配置完成，已开放HTTPS服务端口443和HTTP端口80"
}

# 测试SSL配置
test_ssl_config() {
    log_step "测试SSL配置..."
    
    # 检查Apache配置语法
    if httpd -t; then
        log_info "Apache配置语法正确"
    else
        log_error "Apache配置语法错误"
        return 1
    fi
    
    # 重启Apache
    systemctl restart httpd
    
    # 等待服务启动
    sleep 5
    
    # 测试SSL连接
    if openssl s_client -connect $CURRENT_IP:443 -servername $SSL_DOMAIN < /dev/null 2>/dev/null | grep -q "CONNECTED"; then
        log_info "SSL连接测试成功"
    else
        log_warn "SSL连接测试失败"
    fi
    
    # 测试HTTP重定向
    if curl -s -I http://$CURRENT_IP | grep -q "301\|302"; then
        log_info "HTTP到HTTPS重定向测试成功"
    else
        log_warn "HTTP到HTTPS重定向测试失败"
    fi
}

# 显示配置信息
show_config_info() {
    log_step "SSL Web服务器配置完成！"
    echo
    echo "=================================="
    echo "SSL Web服务器配置信息:"
    echo "=================================="
    echo "服务器IP: $CURRENT_IP"
    echo "SSL域名: $SSL_DOMAIN"
    echo "HTTPS端口: 443"
    echo "HTTP端口: 80 (重定向到HTTPS)"
    echo "网络接口: $INTERFACE"
    echo "网关: $GATEWAY"
    echo
    echo "SSL证书信息:"
    echo "证书文件: $CERT_FILE"
    echo "私钥文件: $KEY_FILE"
    echo "证书类型: 自签名证书"
    echo "有效期: 1年"
    echo
    echo "访问地址:"
    echo "https://$CURRENT_IP (可能有证书警告)"
    echo "https://$SSL_DOMAIN (需要DNS解析)"
    echo "http://$CURRENT_IP (自动重定向到HTTPS)"
    echo
    echo "测试页面:"
    echo "https://$CURRENT_IP/ssl-status.html"
    echo
    echo "重要目录:"
    echo "网站根目录: /var/www/$SSL_DOMAIN"
    echo "SSL配置: /etc/httpd/conf.d/$SSL_DOMAIN-ssl.conf"
    echo "证书目录: $CERT_DIR"
    echo "私钥目录: $PRIVATE_KEY_DIR"
    echo
    echo "注意事项:"
    echo "1. 这是自签名证书，浏览器会显示安全警告"
    echo "2. 生产环境请使用CA颁发的证书"
    echo "3. 可以使用Let's Encrypt获取免费证书"
    echo "=================================="
}

# 主函数
main() {
    log_info "开始配置SSL Web服务器..."
    
    check_root
    get_network_config
    update_system
    install_apache_ssl
    create_ssl_dirs
    generate_ssl_certificate
    configure_ssl_vhost
    create_ssl_test_pages
    configure_firewall
    test_ssl_config
    show_config_info
    
    log_info "SSL Web服务器配置完成！"
}

# 执行主函数
main "$@"
