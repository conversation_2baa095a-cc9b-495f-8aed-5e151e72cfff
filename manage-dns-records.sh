#!/bin/bash

# DNS记录管理脚本
# 用于在DNS服务器部署后添加、修改或删除DNS记录

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_title() {
    echo -e "${CYAN}[TITLE]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查BIND服务是否运行
check_bind_service() {
    if ! systemctl is-active --quiet named; then
        log_error "BIND DNS服务未运行，请先部署DNS服务器"
        exit 1
    fi
    
    log_info "BIND DNS服务运行正常"
}

# 查找域名配置文件
find_domain_config() {
    log_step "查找域名配置文件..."
    
    # 从named.conf中查找域名
    DOMAINS=$(grep -E "zone.*IN" /etc/named.conf | grep -v "in-addr.arpa" | awk '{print $2}' | tr -d '"')
    
    if [[ -z "$DOMAINS" ]]; then
        log_error "未找到配置的域名"
        exit 1
    fi
    
    echo "已配置的域名:"
    local i=1
    for domain in $DOMAINS; do
        echo "$i. $domain"
        ((i++))
    done
    
    read -p "请选择要管理的域名编号: " domain_choice
    SELECTED_DOMAIN=$(echo $DOMAINS | awk -v n=$domain_choice '{print $n}')
    
    if [[ -z "$SELECTED_DOMAIN" ]]; then
        log_error "无效的域名选择"
        exit 1
    fi
    
    ZONE_FILE="/var/named/${SELECTED_DOMAIN}.zone"
    
    if [[ ! -f "$ZONE_FILE" ]]; then
        log_error "域名配置文件不存在: $ZONE_FILE"
        exit 1
    fi
    
    log_info "选择的域名: $SELECTED_DOMAIN"
    log_info "配置文件: $ZONE_FILE"
}

# 显示当前DNS记录
show_current_records() {
    log_step "当前DNS记录:"
    echo "=================================="
    cat "$ZONE_FILE" | grep -E "IN\s+(A|AAAA|CNAME|MX|TXT|PTR)" | nl
    echo "=================================="
}

# 显示管理选项
show_management_options() {
    echo
    echo "DNS记录管理选项:"
    echo "1. 查看当前记录"
    echo "2. 添加A记录"
    echo "3. 添加CNAME记录"
    echo "4. 添加MX记录"
    echo "5. 删除记录"
    echo "6. 修改记录"
    echo "7. 重新加载DNS配置"
    echo "8. 测试DNS解析"
    echo "9. 退出"
    echo
}

# 获取用户选择
get_user_choice() {
    while true; do
        read -p "请选择操作 (1-9): " choice
        case $choice in
            [1-9])
                return $choice
                ;;
            *)
                log_warn "无效选择，请输入1-9之间的数字"
                ;;
        esac
    done
}

# 备份配置文件
backup_zone_file() {
    local backup_file="${ZONE_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$ZONE_FILE" "$backup_file"
    log_info "配置文件已备份到: $backup_file"
}

# 更新序列号
update_serial() {
    local current_serial=$(grep -E "^\s*[0-9]+\s*;\s*Serial" "$ZONE_FILE" | awk '{print $1}')
    local new_serial=$((current_serial + 1))
    
    sed -i "s/${current_serial}\s*;\s*Serial/${new_serial}        ; Serial/" "$ZONE_FILE"
    log_info "序列号已更新: $current_serial -> $new_serial"
}

# 添加A记录
add_a_record() {
    log_step "添加A记录"
    
    read -p "请输入主机名 (例如: web1): " hostname
    read -p "请输入IP地址: " ip_address
    
    # 验证IP地址格式
    if ! [[ $ip_address =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        log_error "无效的IP地址格式"
        return 1
    fi
    
    # 检查记录是否已存在
    if grep -q "^${hostname}\s" "$ZONE_FILE"; then
        log_warn "记录已存在，是否覆盖? (y/n)"
        read -p "> " confirm
        if [[ "$confirm" != "y" ]]; then
            return 0
        fi
        # 删除现有记录
        sed -i "/^${hostname}\s/d" "$ZONE_FILE"
    fi
    
    backup_zone_file
    
    # 在Web服务器记录部分添加新记录
    sed -i "/; Web服务器记录/a ${hostname}                IN  A       ${ip_address}" "$ZONE_FILE"
    
    update_serial
    log_info "A记录添加成功: $hostname -> $ip_address"
}

# 添加CNAME记录
add_cname_record() {
    log_step "添加CNAME记录"
    
    read -p "请输入别名 (例如: blog): " alias_name
    read -p "请输入目标主机名 (例如: www): " target_host
    
    # 检查记录是否已存在
    if grep -q "^${alias_name}\s" "$ZONE_FILE"; then
        log_warn "记录已存在，是否覆盖? (y/n)"
        read -p "> " confirm
        if [[ "$confirm" != "y" ]]; then
            return 0
        fi
        sed -i "/^${alias_name}\s/d" "$ZONE_FILE"
    fi
    
    backup_zone_file
    
    # 添加CNAME记录
    sed -i "/; CNAME records/a ${alias_name}            IN  CNAME   ${target_host}" "$ZONE_FILE"
    
    update_serial
    log_info "CNAME记录添加成功: $alias_name -> $target_host"
}

# 添加MX记录
add_mx_record() {
    log_step "添加MX记录"
    
    read -p "请输入优先级 (例如: 10): " priority
    read -p "请输入邮件服务器 (例如: mail): " mail_server
    
    backup_zone_file
    
    # 添加MX记录
    sed -i "/; MX record/a @       IN  MX  ${priority}  ${mail_server}.${SELECTED_DOMAIN}." "$ZONE_FILE"
    
    update_serial
    log_info "MX记录添加成功: 优先级 $priority -> $mail_server"
}

# 删除记录
delete_record() {
    log_step "删除DNS记录"
    
    show_current_records
    
    read -p "请输入要删除的主机名: " hostname
    
    if ! grep -q "^${hostname}\s" "$ZONE_FILE"; then
        log_error "未找到指定的记录: $hostname"
        return 1
    fi
    
    backup_zone_file
    
    # 删除记录
    sed -i "/^${hostname}\s/d" "$ZONE_FILE"
    
    update_serial
    log_info "记录删除成功: $hostname"
}

# 重新加载DNS配置
reload_dns_config() {
    log_step "重新加载DNS配置..."
    
    # 检查配置文件语法
    if ! named-checkconf; then
        log_error "主配置文件语法错误"
        return 1
    fi
    
    if ! named-checkzone "$SELECTED_DOMAIN" "$ZONE_FILE"; then
        log_error "区域文件语法错误"
        return 1
    fi
    
    # 重新加载配置
    if systemctl reload named; then
        log_info "DNS配置重新加载成功"
    else
        log_error "DNS配置重新加载失败"
        return 1
    fi
}

# 测试DNS解析
test_dns_resolution() {
    log_step "测试DNS解析..."
    
    local dns_server=$(hostname -I | awk '{print $1}')
    
    echo "测试域名解析:"
    echo "=================================="
    
    # 测试主域名
    echo "测试主域名: $SELECTED_DOMAIN"
    if nslookup "$SELECTED_DOMAIN" "$dns_server" > /dev/null 2>&1; then
        echo "✅ $SELECTED_DOMAIN 解析成功"
    else
        echo "❌ $SELECTED_DOMAIN 解析失败"
    fi
    
    # 测试www记录
    echo "测试www记录: www.$SELECTED_DOMAIN"
    if nslookup "www.$SELECTED_DOMAIN" "$dns_server" > /dev/null 2>&1; then
        echo "✅ www.$SELECTED_DOMAIN 解析成功"
    else
        echo "❌ www.$SELECTED_DOMAIN 解析失败"
    fi
    
    # 测试其他A记录
    local a_records=$(grep -E "^\w+\s+IN\s+A" "$ZONE_FILE" | awk '{print $1}' | grep -v "@")
    for record in $a_records; do
        if [[ "$record" != "www" ]]; then
            echo "测试记录: $record.$SELECTED_DOMAIN"
            if nslookup "$record.$SELECTED_DOMAIN" "$dns_server" > /dev/null 2>&1; then
                echo "✅ $record.$SELECTED_DOMAIN 解析成功"
            else
                echo "❌ $record.$SELECTED_DOMAIN 解析失败"
            fi
        fi
    done
    
    echo "=================================="
}

# 主函数
main() {
    log_title "DNS记录管理工具"
    echo
    
    check_root
    check_bind_service
    find_domain_config
    
    while true; do
        show_management_options
        get_user_choice
        choice=$?
        
        case $choice in
            1) # 查看当前记录
                show_current_records
                ;;
            2) # 添加A记录
                add_a_record
                ;;
            3) # 添加CNAME记录
                add_cname_record
                ;;
            4) # 添加MX记录
                add_mx_record
                ;;
            5) # 删除记录
                delete_record
                ;;
            6) # 修改记录
                log_info "请先删除旧记录，然后添加新记录"
                ;;
            7) # 重新加载DNS配置
                reload_dns_config
                ;;
            8) # 测试DNS解析
                test_dns_resolution
                ;;
            9) # 退出
                log_info "退出DNS记录管理工具"
                exit 0
                ;;
        esac
        
        echo
        read -p "按回车键继续..."
        echo
    done
}

# 执行主函数
main "$@"
