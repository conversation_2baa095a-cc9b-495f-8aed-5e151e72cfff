#!/bin/bash

# 网络配置检查脚本
# 在运行主配置脚本前检查网络环境

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 网络环境检查 ===${NC}"
echo

# 检查网络接口
echo -e "${YELLOW}[检查] 网络接口信息${NC}"
INTERFACE=$(ip route | grep default | awk '{print $5}' | head -1)
if [ -n "$INTERFACE" ]; then
    echo -e "${GREEN}✅ 默认网络接口: $INTERFACE${NC}"
    
    # 显示接口详细信息
    ip addr show $INTERFACE | grep -E "inet |link/"
else
    echo -e "${RED}❌ 未找到默认网络接口${NC}"
fi
echo

# 检查IP地址
echo -e "${YELLOW}[检查] IP地址配置${NC}"
CURRENT_IP=$(ip route get ******* 2>/dev/null | awk '{print $7; exit}')
if [ -n "$CURRENT_IP" ]; then
    echo -e "${GREEN}✅ 当前IP地址: $CURRENT_IP${NC}"
    
    # 检查IP类型
    if [[ $CURRENT_IP =~ ^192\.168\. ]] || [[ $CURRENT_IP =~ ^10\. ]] || [[ $CURRENT_IP =~ ^172\.(1[6-9]|2[0-9]|3[0-1])\. ]]; then
        echo -e "${BLUE}ℹ️  这是一个私有IP地址${NC}"
    else
        echo -e "${BLUE}ℹ️  这是一个公网IP地址${NC}"
    fi
else
    echo -e "${RED}❌ 无法获取IP地址${NC}"
fi
echo

# 检查网关
echo -e "${YELLOW}[检查] 网关配置${NC}"
GATEWAY=$(ip route | grep default | awk '{print $3}' | head -1)
if [ -n "$GATEWAY" ]; then
    echo -e "${GREEN}✅ 默认网关: $GATEWAY${NC}"
    
    # 测试网关连通性
    if ping -c 1 -W 3 $GATEWAY >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 网关连通性正常${NC}"
    else
        echo -e "${RED}❌ 网关连通性异常${NC}"
    fi
else
    echo -e "${RED}❌ 未找到默认网关${NC}"
fi
echo

# 检查DNS解析
echo -e "${YELLOW}[检查] DNS解析${NC}"
if nslookup google.com >/dev/null 2>&1; then
    echo -e "${GREEN}✅ DNS解析正常${NC}"
    
    # 显示当前DNS服务器
    DNS_SERVERS=$(grep nameserver /etc/resolv.conf | awk '{print $2}' | tr '\n' ' ')
    echo -e "${BLUE}ℹ️  当前DNS服务器: $DNS_SERVERS${NC}"
else
    echo -e "${RED}❌ DNS解析异常${NC}"
fi
echo

# 检查互联网连接
echo -e "${YELLOW}[检查] 互联网连接${NC}"
if ping -c 1 -W 5 ******* >/dev/null 2>&1; then
    echo -e "${GREEN}✅ 互联网连接正常${NC}"
else
    echo -e "${RED}❌ 互联网连接异常${NC}"
fi
echo

# 检查防火墙状态
echo -e "${YELLOW}[检查] 防火墙状态${NC}"
if systemctl is-active --quiet firewalld; then
    echo -e "${GREEN}✅ 防火墙服务运行中${NC}"
    
    # 显示当前开放的服务
    OPEN_SERVICES=$(firewall-cmd --list-services 2>/dev/null | tr ' ' '\n' | head -5 | tr '\n' ' ')
    if [ -n "$OPEN_SERVICES" ]; then
        echo -e "${BLUE}ℹ️  已开放的服务: $OPEN_SERVICES${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  防火墙服务未运行${NC}"
fi
echo

# 检查SELinux状态
echo -e "${YELLOW}[检查] SELinux状态${NC}"
if command -v getenforce >/dev/null 2>&1; then
    SELINUX_STATUS=$(getenforce)
    case $SELINUX_STATUS in
        "Enforcing")
            echo -e "${YELLOW}⚠️  SELinux状态: $SELINUX_STATUS (强制模式)${NC}"
            echo -e "${BLUE}ℹ️  可能需要配置SELinux策略${NC}"
            ;;
        "Permissive")
            echo -e "${YELLOW}⚠️  SELinux状态: $SELINUX_STATUS (宽松模式)${NC}"
            ;;
        "Disabled")
            echo -e "${GREEN}✅ SELinux状态: $SELINUX_STATUS${NC}"
            ;;
    esac
else
    echo -e "${BLUE}ℹ️  SELinux未安装或不可用${NC}"
fi
echo

# 检查系统版本
echo -e "${YELLOW}[检查] 系统版本${NC}"
if [ -f /etc/os-release ]; then
    OS_NAME=$(grep "^NAME=" /etc/os-release | cut -d'"' -f2)
    OS_VERSION=$(grep "^VERSION=" /etc/os-release | cut -d'"' -f2)
    echo -e "${GREEN}✅ 操作系统: $OS_NAME $OS_VERSION${NC}"
    
    # 检查是否为CentOS 9
    if echo "$OS_NAME" | grep -qi "centos" && echo "$OS_VERSION" | grep -q "9"; then
        echo -e "${GREEN}✅ 系统版本兼容${NC}"
    else
        echo -e "${YELLOW}⚠️  脚本专为CentOS 9设计，当前系统可能不完全兼容${NC}"
    fi
else
    echo -e "${RED}❌ 无法确定系统版本${NC}"
fi
echo

# 检查磁盘空间
echo -e "${YELLOW}[检查] 磁盘空间${NC}"
ROOT_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$ROOT_USAGE" -lt 80 ]; then
    echo -e "${GREEN}✅ 根分区使用率: ${ROOT_USAGE}%${NC}"
else
    echo -e "${YELLOW}⚠️  根分区使用率较高: ${ROOT_USAGE}%${NC}"
fi

# 显示可用空间
AVAILABLE_SPACE=$(df -h / | awk 'NR==2 {print $4}')
echo -e "${BLUE}ℹ️  可用空间: $AVAILABLE_SPACE${NC}"
echo

# 检查内存
echo -e "${YELLOW}[检查] 内存状态${NC}"
TOTAL_MEM=$(free -h | awk 'NR==2{print $2}')
AVAILABLE_MEM=$(free -h | awk 'NR==2{print $7}')
echo -e "${GREEN}✅ 总内存: $TOTAL_MEM${NC}"
echo -e "${BLUE}ℹ️  可用内存: $AVAILABLE_MEM${NC}"
echo

# 总结
echo -e "${BLUE}=== 检查总结 ===${NC}"
echo -e "${GREEN}网络配置检查完成！${NC}"
echo
echo -e "${BLUE}重要信息总结:${NC}"
echo "• IP地址: $CURRENT_IP"
echo "• 网络接口: $INTERFACE"
echo "• 网关: $GATEWAY"
echo "• 系统: $OS_NAME $OS_VERSION"
echo
echo -e "${YELLOW}下一步操作:${NC}"
echo "1. 如果所有检查都通过，可以运行配置脚本"
echo "2. 运行前请确保有root权限: sudo ./server0X-xxx-setup.sh"
echo "3. 建议先运行 ./setup-permissions.sh 设置脚本权限"
echo
echo -e "${BLUE}可用的配置脚本:${NC}"
echo "• ./server01-dns-setup.sh    - DNS服务器"
echo "• ./server02-web1-setup.sh   - Web服务器1 (LAMP)"
echo "• ./server03-web2-setup.sh   - Web服务器2 (SSL)"
