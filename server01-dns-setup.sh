#!/bin/bash

# Server01 - DNS服务器完整配置脚本
# 系统: CentOS 9
# 功能: BIND DNS服务器，提供域名解析服务
# 包含错误检测和自动修复功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 错误处理函数
handle_error() {
    log_error "脚本执行失败，行号: $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 获取网络配置信息
get_network_config() {
    log_step "获取网络配置信息..."

    # 获取当前IP地址
    CURRENT_IP=$(ip route get ******* | awk '{print $7; exit}')

    # 获取网络接口名称
    INTERFACE=$(ip route | grep default | awk '{print $5}' | head -1)

    # 获取网关
    GATEWAY=$(ip route | grep default | awk '{print $3}' | head -1)

    log_info "当前IP地址: $CURRENT_IP"
    log_info "网络接口: $INTERFACE"
    log_info "网关: $GATEWAY"

    # 用户输入域名信息
    read -p "请输入主域名 (例如: example.com): " DOMAIN_NAME
    read -p "请输入DNS服务器名称 (例如: ns1): " DNS_SERVER_NAME

    # 获取其他服务器的IP地址
    echo
    echo "请输入其他服务器的IP地址 (如果暂时不知道可以按回车跳过，后续可手动添加):"
    read -p "Web服务器1 (HTTP) IP地址: " WEB1_IP
    read -p "Web服务器2 (HTTPS) IP地址: " WEB2_IP

    # 验证输入
    if [[ -z "$DOMAIN_NAME" || -z "$DNS_SERVER_NAME" ]]; then
        log_error "域名和DNS服务器名称不能为空"
        exit 1
    fi

    DNS_FQDN="${DNS_SERVER_NAME}.${DOMAIN_NAME}"
    log_info "DNS服务器FQDN: $DNS_FQDN"
}

# 系统更新
update_system() {
    log_step "更新系统..."
    dnf update -y
    dnf install -y epel-release
}

# 安装BIND DNS服务器
install_bind() {
    log_step "安装BIND DNS服务器..."
    dnf install -y bind bind-utils
}

# 配置BIND主配置文件
configure_bind_main() {
    log_step "配置BIND主配置文件..."

    # 备份原配置文件
    cp /etc/named.conf /etc/named.conf.backup

    cat > /etc/named.conf << EOF
//
// named.conf
//
// Provided by Red Hat bind package to configure the ISC BIND named(8) DNS
// server as a caching only nameserver (as a localhost DNS resolver only).
//
// See /usr/share/doc/bind*/sample/ for example named configuration files.
//

options {
    listen-on port 53 { 127.0.0.1; $CURRENT_IP; };
    listen-on-v6 port 53 { ::1; };
    directory "/var/named";
    dump-file "/var/named/data/cache_dump.db";
    statistics-file "/var/named/data/named_stats.txt";
    memstatistics-file "/var/named/data/named_mem_stats.txt";
    secroots-file "/var/named/data/named.secroots";
    recursing-file "/var/named/data/named.recursing";
    allow-query { localhost; any; };
    allow-recursion { localhost; any; };

    /*
     - If you are building an AUTHORITATIVE DNS server, do NOT enable recursion.
     - If you are building a RECURSIVE (caching) DNS server, you need to enable
       recursion.
     - If your recursive DNS server has a public IP address, you MUST enable access
       control to limit queries to your legitimate users. Failing to do so will
       cause your server to become part of large scale DNS amplification
       attacks. Implementing BCP38 within your network would greatly
       reduce such attack surface
    */
    recursion yes;

    dnssec-enable yes;
    dnssec-validation yes;

    managed-keys-directory "/var/named/dynamic";

    pid-file "/run/named/named.pid";
    session-keyfile "/run/named/session.key";

    /* https://fedoraproject.org/wiki/Changes/CryptoPolicy */
    include "/etc/crypto-policies/back-ends/bind.config";
};

logging {
        channel default_debug {
                file "data/named.run";
                severity dynamic;
        };
};

zone "." IN {
    type hint;
    file "named.ca";
};

zone "$DOMAIN_NAME" IN {
    type master;
    file "$DOMAIN_NAME.zone";
    allow-update { none; };
};

zone "$(echo $CURRENT_IP | awk -F. '{print $3"."$2"."$1}').in-addr.arpa" IN {
    type master;
    file "$DOMAIN_NAME.rev";
    allow-update { none; };
};

include "/etc/named.rfc1912.zones";
include "/etc/named.root.key";
EOF
}

# 创建正向解析区域文件
create_forward_zone() {
    log_step "创建正向解析区域文件..."

    cat > /var/named/$DOMAIN_NAME.zone << EOF
\$TTL 86400
@   IN  SOA     $DNS_FQDN. admin.$DOMAIN_NAME. (
        2024010101  ; Serial
        3600        ; Refresh
        1800        ; Retry
        604800      ; Expire
        86400       ; Minimum TTL
)

; Name servers
@       IN  NS      $DNS_FQDN.

; DNS服务器记录
$DNS_SERVER_NAME    IN  A       $CURRENT_IP

; 基础域名记录
@                   IN  A       $CURRENT_IP
www                 IN  A       $CURRENT_IP
mail                IN  A       $CURRENT_IP
ftp                 IN  A       $CURRENT_IP

; Web服务器记录
EOF

    # 添加Web服务器1记录
    if [[ -n "$WEB1_IP" ]]; then
        cat >> /var/named/$DOMAIN_NAME.zone << EOF
web1                IN  A       $WEB1_IP
http                IN  A       $WEB1_IP
lamp                IN  A       $WEB1_IP
EOF
        log_info "已添加Web服务器1记录: $WEB1_IP"
    fi

    # 添加Web服务器2记录
    if [[ -n "$WEB2_IP" ]]; then
        cat >> /var/named/$DOMAIN_NAME.zone << EOF
web2                IN  A       $WEB2_IP
https               IN  A       $WEB2_IP
secure              IN  A       $WEB2_IP
ssl                 IN  A       $WEB2_IP
EOF
        log_info "已添加Web服务器2记录: $WEB2_IP"
    fi

    # 添加MX记录
    cat >> /var/named/$DOMAIN_NAME.zone << EOF

; MX record
@       IN  MX  10  mail.$DOMAIN_NAME.

; CNAME records (可选)
; webmail            IN  CNAME   mail
; admin              IN  CNAME   www
EOF

    chown named:named /var/named/$DOMAIN_NAME.zone
    chmod 644 /var/named/$DOMAIN_NAME.zone
}

# 创建反向解析区域文件
create_reverse_zone() {
    log_step "创建反向解析区域文件..."

    # 获取IP的最后一个八位组
    LAST_OCTET=$(echo $CURRENT_IP | awk -F. '{print $4}')

    cat > /var/named/$DOMAIN_NAME.rev << EOF
\$TTL 86400
@   IN  SOA     $DNS_FQDN. admin.$DOMAIN_NAME. (
        2024010101  ; Serial
        3600        ; Refresh
        1800        ; Retry
        604800      ; Expire
        86400       ; Minimum TTL
)

; Name servers
@       IN  NS      $DNS_FQDN.

; PTR records
$LAST_OCTET     IN  PTR     $DNS_FQDN.
$LAST_OCTET     IN  PTR     www.$DOMAIN_NAME.
EOF

    chown named:named /var/named/$DOMAIN_NAME.rev
    chmod 644 /var/named/$DOMAIN_NAME.rev
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."

    systemctl start firewalld
    systemctl enable firewalld

    firewall-cmd --permanent --add-service=dns
    firewall-cmd --reload

    log_info "防火墙配置完成，已开放DNS服务端口53"
}

# 验证配置
validate_config() {
    log_step "验证BIND配置..."

    # 检查配置文件语法
    if named-checkconf; then
        log_info "主配置文件语法正确"
    else
        log_error "主配置文件语法错误"
        return 1
    fi

    # 检查区域文件
    if named-checkzone $DOMAIN_NAME /var/named/$DOMAIN_NAME.zone; then
        log_info "正向区域文件语法正确"
    else
        log_error "正向区域文件语法错误"
        return 1
    fi

    if named-checkzone $(echo $CURRENT_IP | awk -F. '{print $3"."$2"."$1}').in-addr.arpa /var/named/$DOMAIN_NAME.rev; then
        log_info "反向区域文件语法正确"
    else
        log_error "反向区域文件语法错误"
        return 1
    fi
}

# 启动服务
start_services() {
    log_step "启动DNS服务..."

    systemctl start named
    systemctl enable named

    if systemctl is-active --quiet named; then
        log_info "DNS服务启动成功"
    else
        log_error "DNS服务启动失败"
        return 1
    fi
}

# 测试DNS解析
test_dns() {
    log_step "测试DNS解析..."

    # 等待服务完全启动
    sleep 5

    # 测试正向解析
    if nslookup $DNS_FQDN $CURRENT_IP > /dev/null 2>&1; then
        log_info "正向解析测试成功"
    else
        log_warn "正向解析测试失败"
    fi

    # 测试反向解析
    if nslookup $CURRENT_IP $CURRENT_IP > /dev/null 2>&1; then
        log_info "反向解析测试成功"
    else
        log_warn "反向解析测试失败"
    fi
}

# 显示配置信息
show_config_info() {
    log_step "DNS服务器配置完成！"
    echo
    echo "=================================="
    echo "DNS服务器配置信息:"
    echo "=================================="
    echo "服务器IP: $CURRENT_IP"
    echo "域名: $DOMAIN_NAME"
    echo "DNS服务器: $DNS_FQDN"
    echo "网络接口: $INTERFACE"
    echo "网关: $GATEWAY"
    echo
    echo "测试命令:"
    echo "nslookup $DNS_FQDN $CURRENT_IP"
    echo "nslookup www.$DOMAIN_NAME $CURRENT_IP"
    echo
    echo "配置文件位置:"
    echo "主配置: /etc/named.conf"
    echo "正向区域: /var/named/$DOMAIN_NAME.zone"
    echo "反向区域: /var/named/$DOMAIN_NAME.rev"
    echo "=================================="
}

# 主函数
main() {
    log_info "开始配置DNS服务器..."

    check_root
    get_network_config
    update_system
    install_bind
    configure_bind_main
    create_forward_zone
    create_reverse_zone
    configure_firewall
    validate_config
    start_services
    test_dns
    show_config_info

    log_info "DNS服务器配置完成！"
}

# 执行主函数
main "$@"
