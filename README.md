# CentOS 9 服务器配置脚本集合

本项目包含三个完整的服务器配置脚本，用于在CentOS 9系统上快速部署不同类型的服务器。

## 📋 脚本概览

### 🌐 Server01 - DNS服务器 (`server01-dns-setup.sh`)
- **系统**: CentOS 9
- **功能**: BIND DNS服务器
- **服务**: 域名解析服务
- **端口**: 53 (DNS)

### 🖥️ Server02 - Web服务器1 (`server02-web1-setup.sh`)
- **系统**: CentOS 9
- **功能**: Apache + MariaDB + PHP
- **服务**: 完整的LAMP环境
- **端口**: 80 (HTTP)

### 🔒 Server03 - Web服务器2 (`server03-web2-setup.sh`)
- **系统**: CentOS 9
- **功能**: Apache + SSL
- **服务**: HTTPS安全网站
- **端口**: 443 (HTTPS), 80 (重定向)

## 🚀 快速开始

### 前置要求
- CentOS 9 系统
- Root权限
- 网络连接
- 基本的Linux命令知识

### 使用步骤

1. **下载脚本**
   ```bash
   # 克隆或下载脚本文件到服务器
   chmod +x *.sh
   ```

2. **运行对应的脚本**
   ```bash
   # DNS服务器
   sudo ./server01-dns-setup.sh
   
   # Web服务器1 (LAMP)
   sudo ./server02-web1-setup.sh
   
   # Web服务器2 (SSL)
   sudo ./server03-web2-setup.sh
   ```

## 📖 详细说明

### 🌐 DNS服务器配置 (Server01)

**功能特性:**
- ✅ BIND DNS服务器安装和配置
- ✅ 正向和反向DNS解析
- ✅ 自定义域名配置
- ✅ 防火墙自动配置
- ✅ 配置验证和测试
- ✅ 错误检测和自动修复

**配置过程:**
1. 系统更新和BIND安装
2. 获取网络配置信息
3. 用户输入域名信息
4. 配置主配置文件 `/etc/named.conf`
5. 创建正向解析区域文件
6. 创建反向解析区域文件
7. 防火墙配置 (开放端口53)
8. 服务启动和自启动配置
9. DNS解析测试

**输入信息:**
- 主域名 (例如: example.com)
- DNS服务器名称 (例如: ns1)

**测试命令:**
```bash
nslookup ns1.example.com [服务器IP]
nslookup www.example.com [服务器IP]
```

### 🖥️ Web服务器1配置 (Server02)

**功能特性:**
- ✅ Apache HTTP服务器
- ✅ MariaDB数据库服务器
- ✅ PHP及常用模块
- ✅ 虚拟主机配置
- ✅ 数据库用户和权限设置
- ✅ 测试页面自动创建
- ✅ 防火墙配置 (端口80)

**配置过程:**
1. 系统更新和软件包安装
2. Apache安装和配置
3. PHP安装和优化配置
4. MariaDB安装和安全配置
5. 数据库和用户创建
6. 虚拟主机配置
7. 测试页面创建
8. 防火墙配置
9. 服务测试

**输入信息:**
- 网站域名
- MySQL root密码
- 网站数据库名称
- 数据库用户名和密码

**访问地址:**
- `http://[服务器IP]/` - 主页
- `http://[服务器IP]/phpinfo.php` - PHP信息
- `http://[服务器IP]/dbtest.php` - 数据库连接测试

### 🔒 Web服务器2配置 (Server03)

**功能特性:**
- ✅ Apache HTTP服务器
- ✅ SSL/TLS加密支持
- ✅ 自签名证书生成
- ✅ HTTPS虚拟主机配置
- ✅ HTTP到HTTPS自动重定向
- ✅ 安全头部配置 (HSTS, X-Frame-Options等)
- ✅ SSL测试页面

**配置过程:**
1. 系统更新和Apache+SSL模块安装
2. SSL证书目录创建
3. 自签名证书生成
4. SSL虚拟主机配置
5. HTTP重定向配置
6. 安全头部设置
7. SSL测试页面创建
8. 防火墙配置 (端口443和80)
9. SSL连接测试

**输入信息:**
- SSL网站域名
- 组织名称
- 国家代码
- 省份和城市

**访问地址:**
- `https://[服务器IP]/` - SSL主页
- `https://[服务器IP]/ssl-status.html` - SSL状态检查
- `http://[服务器IP]/` - 自动重定向到HTTPS

## 🔧 网络配置说明

### 动态IP原则
所有脚本都遵循**不改变动态IP原则**：
- 自动检测当前IP地址
- 不修改网络接口配置
- 保持现有的网络设置
- 支持DHCP环境

### IP绑定方式
脚本会自动：
1. 检测当前活动的网络接口
2. 获取当前IP地址和网关
3. 将服务绑定到检测到的IP地址
4. 配置防火墙规则

### 手动IP配置
如果需要绑定特定IP，可以在脚本运行前修改以下变量：
```bash
# 在脚本中找到这行并修改
CURRENT_IP="你的IP地址"
```

## 🛡️ 安全特性

### 防火墙配置
- **DNS服务器**: 开放端口53 (DNS)
- **Web服务器1**: 开放端口80 (HTTP)
- **Web服务器2**: 开放端口443 (HTTPS) 和 80 (重定向)

### SSL安全配置
Web服务器2包含以下安全特性：
- TLS 1.2+ 协议支持
- 强加密套件配置
- HSTS (HTTP Strict Transport Security)
- 安全头部设置
- 自动HTTP到HTTPS重定向

## 🔍 故障排除

### 常见问题

1. **权限错误**
   ```bash
   # 确保以root权限运行
   sudo ./script-name.sh
   ```

2. **网络连接问题**
   ```bash
   # 检查网络连接
   ping *******
   # 检查DNS解析
   nslookup google.com
   ```

3. **服务启动失败**
   ```bash
   # 检查服务状态
   systemctl status httpd
   systemctl status named
   systemctl status mariadb
   
   # 查看日志
   journalctl -u httpd -f
   journalctl -u named -f
   ```

4. **防火墙问题**
   ```bash
   # 检查防火墙状态
   firewall-cmd --list-all
   
   # 临时关闭防火墙测试
   systemctl stop firewalld
   ```

### 日志文件位置
- **Apache**: `/var/log/httpd/`
- **DNS**: `/var/log/messages`
- **MariaDB**: `/var/log/mariadb/`

## 📝 配置文件位置

### DNS服务器
- 主配置: `/etc/named.conf`
- 区域文件: `/var/named/[域名].zone`
- 反向区域: `/var/named/[域名].rev`

### Web服务器1
- Apache配置: `/etc/httpd/conf.d/[域名].conf`
- PHP配置: `/etc/php.ini`
- 网站目录: `/var/www/[域名]/`

### Web服务器2
- SSL配置: `/etc/httpd/conf.d/[域名]-ssl.conf`
- SSL证书: `/etc/ssl/certs/[域名].crt`
- SSL私钥: `/etc/ssl/private/[域名].key`
- 网站目录: `/var/www/[域名]/`

## 🔄 维护和更新

### 定期维护任务
```bash
# 系统更新
dnf update -y

# 重启服务
systemctl restart httpd
systemctl restart named
systemctl restart mariadb

# 检查服务状态
systemctl status httpd named mariadb
```

### SSL证书更新
自签名证书有效期为1年，到期前需要重新生成：
```bash
# 重新运行SSL配置部分
./server03-web2-setup.sh
```

## 📞 支持

如果在使用过程中遇到问题：
1. 检查系统日志: `journalctl -f`
2. 验证网络连接
3. 确认防火墙设置
4. 检查服务状态

## 📄 许可证

本项目采用MIT许可证，可自由使用和修改。

---

**注意**: 这些脚本主要用于测试和开发环境。在生产环境中使用前，请根据具体需求进行安全加固和性能优化。
