#!/bin/bash

# 集成部署脚本 - 按正确顺序部署所有服务器
# 支持DNS服务器与Web服务器的关联配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_title() {
    echo -e "${CYAN}[TITLE]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查脚本文件是否存在
check_scripts() {
    log_step "检查脚本文件..."
    
    local missing_scripts=()
    
    if [[ ! -f "server01-dns-setup.sh" ]]; then
        missing_scripts+=("server01-dns-setup.sh")
    fi
    
    if [[ ! -f "server02-web1-setup.sh" ]]; then
        missing_scripts+=("server02-web1-setup.sh")
    fi
    
    if [[ ! -f "server03-web2-setup.sh" ]]; then
        missing_scripts+=("server03-web2-setup.sh")
    fi
    
    if [[ ${#missing_scripts[@]} -gt 0 ]]; then
        log_error "缺少以下脚本文件:"
        for script in "${missing_scripts[@]}"; do
            echo "  - $script"
        done
        exit 1
    fi
    
    # 设置执行权限
    chmod +x server01-dns-setup.sh
    chmod +x server02-web1-setup.sh
    chmod +x server03-web2-setup.sh
    
    log_info "所有脚本文件检查完成"
}

# 显示部署选项
show_deployment_options() {
    log_title "=== CentOS 9 服务器集成部署 ==="
    echo
    echo "可用的部署选项:"
    echo "1. 完整部署 (DNS + Web1 + Web2) - 推荐"
    echo "2. 仅部署DNS服务器"
    echo "3. 仅部署Web服务器1 (LAMP)"
    echo "4. 仅部署Web服务器2 (SSL)"
    echo "5. 部署DNS + Web服务器1"
    echo "6. 部署DNS + Web服务器2"
    echo "7. 部署Web服务器1 + Web服务器2"
    echo "8. 退出"
    echo
}

# 获取用户选择
get_user_choice() {
    while true; do
        read -p "请选择部署选项 (1-8): " choice
        case $choice in
            [1-8])
                return $choice
                ;;
            *)
                log_warn "无效选择，请输入1-8之间的数字"
                ;;
        esac
    done
}

# 收集网络信息
collect_network_info() {
    log_step "收集网络配置信息..."
    
    # 获取当前IP
    CURRENT_IP=$(ip route get ******* 2>/dev/null | awk '{print $7; exit}')
    INTERFACE=$(ip route | grep default | awk '{print $5}' | head -1)
    GATEWAY=$(ip route | grep default | awk '{print $3}' | head -1)
    
    log_info "当前服务器信息:"
    log_info "IP地址: $CURRENT_IP"
    log_info "网络接口: $INTERFACE"
    log_info "网关: $GATEWAY"
    echo
}

# 收集DNS配置信息
collect_dns_info() {
    log_step "收集DNS服务器配置信息..."
    
    read -p "请输入主域名 (例如: example.com): " DOMAIN_NAME
    read -p "请输入DNS服务器名称 (例如: ns1): " DNS_SERVER_NAME
    
    # 验证输入
    if [[ -z "$DOMAIN_NAME" || -z "$DNS_SERVER_NAME" ]]; then
        log_error "域名和DNS服务器名称不能为空"
        exit 1
    fi
    
    DNS_SERVER_FQDN="${DNS_SERVER_NAME}.${DOMAIN_NAME}"
    log_info "DNS服务器FQDN: $DNS_SERVER_FQDN"
}

# 收集Web服务器信息
collect_web_info() {
    local need_web1=$1
    local need_web2=$2
    
    if [[ "$need_web1" == "true" ]]; then
        log_step "收集Web服务器1配置信息..."
        read -p "请输入Web服务器1的域名 (例如: www.$DOMAIN_NAME): " WEB1_DOMAIN
        read -p "请输入Web服务器1的IP地址 (按回车使用当前IP): " WEB1_IP
        WEB1_IP=${WEB1_IP:-$CURRENT_IP}
        
        read -p "请输入MySQL root密码: " -s MYSQL_ROOT_PASSWORD
        echo
        read -p "请输入数据库名称: " DB_NAME
        read -p "请输入数据库用户名: " DB_USER
        read -p "请输入数据库密码: " -s DB_PASSWORD
        echo
    fi
    
    if [[ "$need_web2" == "true" ]]; then
        log_step "收集Web服务器2配置信息..."
        read -p "请输入Web服务器2的SSL域名 (例如: secure.$DOMAIN_NAME): " WEB2_DOMAIN
        read -p "请输入Web服务器2的IP地址 (按回车使用当前IP): " WEB2_IP
        WEB2_IP=${WEB2_IP:-$CURRENT_IP}
        
        read -p "请输入组织名称: " ORG_NAME
        read -p "请输入国家代码 (例如: CN): " COUNTRY_CODE
        read -p "请输入省份: " STATE
        read -p "请输入城市: " CITY
    fi
}

# 部署DNS服务器
deploy_dns_server() {
    log_title "=== 部署DNS服务器 ==="
    
    # 创建临时配置文件
    cat > /tmp/dns_config.txt << EOF
$DOMAIN_NAME
$DNS_SERVER_NAME
${WEB1_IP:-}
${WEB2_IP:-}
EOF
    
    # 运行DNS配置脚本
    log_info "开始部署DNS服务器..."
    if ./server01-dns-setup.sh < /tmp/dns_config.txt; then
        log_info "DNS服务器部署成功"
        DNS_DEPLOYED=true
    else
        log_error "DNS服务器部署失败"
        return 1
    fi
    
    # 清理临时文件
    rm -f /tmp/dns_config.txt
}

# 部署Web服务器1
deploy_web1_server() {
    log_title "=== 部署Web服务器1 (LAMP) ==="
    
    # 创建临时配置文件
    cat > /tmp/web1_config.txt << EOF
$WEB1_DOMAIN
$MYSQL_ROOT_PASSWORD
$DB_NAME
$DB_USER
$DB_PASSWORD
${DNS_DEPLOYED:+$CURRENT_IP}
${DNS_DEPLOYED:+$DOMAIN_NAME}
EOF
    
    # 运行Web1配置脚本
    log_info "开始部署Web服务器1..."
    if ./server02-web1-setup.sh < /tmp/web1_config.txt; then
        log_info "Web服务器1部署成功"
    else
        log_error "Web服务器1部署失败"
        return 1
    fi
    
    # 清理临时文件
    rm -f /tmp/web1_config.txt
}

# 部署Web服务器2
deploy_web2_server() {
    log_title "=== 部署Web服务器2 (SSL) ==="
    
    # 创建临时配置文件
    cat > /tmp/web2_config.txt << EOF
$WEB2_DOMAIN
$ORG_NAME
$COUNTRY_CODE
$STATE
$CITY
${DNS_DEPLOYED:+$CURRENT_IP}
${DNS_DEPLOYED:+$DOMAIN_NAME}
EOF
    
    # 运行Web2配置脚本
    log_info "开始部署Web服务器2..."
    if ./server03-web2-setup.sh < /tmp/web2_config.txt; then
        log_info "Web服务器2部署成功"
    else
        log_error "Web服务器2部署失败"
        return 1
    fi
    
    # 清理临时文件
    rm -f /tmp/web2_config.txt
}

# 显示部署总结
show_deployment_summary() {
    log_title "=== 部署完成总结 ==="
    echo
    echo "服务器配置信息:"
    echo "=================================="
    
    if [[ "$DNS_DEPLOYED" == "true" ]]; then
        echo "DNS服务器:"
        echo "  - IP地址: $CURRENT_IP"
        echo "  - 域名: $DOMAIN_NAME"
        echo "  - DNS服务器: $DNS_SERVER_FQDN"
        echo "  - 测试命令: nslookup $DOMAIN_NAME $CURRENT_IP"
        echo
    fi
    
    if [[ -n "$WEB1_DOMAIN" ]]; then
        echo "Web服务器1 (LAMP):"
        echo "  - IP地址: $WEB1_IP"
        echo "  - 域名: $WEB1_DOMAIN"
        echo "  - 访问地址: http://$WEB1_IP"
        echo "  - 数据库: $DB_NAME"
        echo
    fi
    
    if [[ -n "$WEB2_DOMAIN" ]]; then
        echo "Web服务器2 (SSL):"
        echo "  - IP地址: $WEB2_IP"
        echo "  - SSL域名: $WEB2_DOMAIN"
        echo "  - 访问地址: https://$WEB2_IP"
        echo "  - 组织: $ORG_NAME"
        echo
    fi
    
    echo "重要提示:"
    echo "1. 如果部署了DNS服务器，其他服务器已配置使用该DNS"
    echo "2. Web服务器的域名需要在DNS中正确解析"
    echo "3. SSL证书为自签名证书，浏览器会显示警告"
    echo "4. 建议在生产环境中使用CA颁发的证书"
    echo "=================================="
}

# 主函数
main() {
    log_title "CentOS 9 服务器集成部署脚本"
    echo
    
    check_root
    check_scripts
    collect_network_info
    
    show_deployment_options
    get_user_choice
    choice=$?
    
    # 初始化变量
    DNS_DEPLOYED=false
    
    case $choice in
        1) # 完整部署
            collect_dns_info
            collect_web_info true true
            deploy_dns_server
            deploy_web1_server
            deploy_web2_server
            ;;
        2) # 仅DNS
            collect_dns_info
            deploy_dns_server
            ;;
        3) # 仅Web1
            read -p "请输入主域名 (用于DNS配置): " DOMAIN_NAME
            collect_web_info true false
            deploy_web1_server
            ;;
        4) # 仅Web2
            read -p "请输入主域名 (用于DNS配置): " DOMAIN_NAME
            collect_web_info false true
            deploy_web2_server
            ;;
        5) # DNS + Web1
            collect_dns_info
            collect_web_info true false
            deploy_dns_server
            deploy_web1_server
            ;;
        6) # DNS + Web2
            collect_dns_info
            collect_web_info false true
            deploy_dns_server
            deploy_web2_server
            ;;
        7) # Web1 + Web2
            read -p "请输入主域名 (用于DNS配置): " DOMAIN_NAME
            collect_web_info true true
            deploy_web1_server
            deploy_web2_server
            ;;
        8) # 退出
            log_info "退出部署"
            exit 0
            ;;
    esac
    
    show_deployment_summary
    log_info "集成部署完成！"
}

# 执行主函数
main "$@"
