#!/bin/bash

# 设置脚本权限和准备部署环境

echo "正在设置脚本权限..."

# 设置所有脚本为可执行
chmod +x server01-dns-setup.sh
chmod +x server02-web1-setup.sh
chmod +x server03-web2-setup.sh
chmod +x deploy-all-servers.sh
chmod +x manage-dns-records.sh
chmod +x check-network.sh

echo "✅ 脚本权限设置完成"

echo ""
echo "=== 可用的脚本工具 ==="
echo ""
echo "🔧 主要配置脚本："
echo "1. ./server01-dns-setup.sh    - DNS服务器配置"
echo "2. ./server02-web1-setup.sh   - Web服务器1 (Apache+MySQL+PHP)"
echo "3. ./server03-web2-setup.sh   - Web服务器2 (Apache+SSL)"
echo ""
echo "🛠️ 管理和辅助工具："
echo "4. ./deploy-all-servers.sh    - 集成部署所有服务器 (推荐)"
echo "5. ./manage-dns-records.sh    - DNS记录管理工具"
echo "6. ./check-network.sh         - 网络环境检查工具"
echo ""
echo "🚀 推荐使用流程："
echo "1. ./check-network.sh         # 检查网络环境"
echo "2. sudo ./deploy-all-servers.sh  # 集成部署"
echo "3. sudo ./manage-dns-records.sh  # 管理DNS记录 (可选)"
echo ""
echo "📝 单独部署方法："
echo "sudo ./server01-dns-setup.sh     # DNS服务器"
echo "sudo ./server02-web1-setup.sh    # Web服务器1"
echo "sudo ./server03-web2-setup.sh    # Web服务器2"
echo ""
echo "注意：需要root权限的脚本请使用sudo运行"
