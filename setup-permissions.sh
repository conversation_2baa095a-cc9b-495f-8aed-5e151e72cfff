#!/bin/bash

# 设置脚本权限和准备部署环境

echo "正在设置脚本权限..."

# 设置所有脚本为可执行
chmod +x server01-dns-setup.sh
chmod +x server02-web1-setup.sh
chmod +x server03-web2-setup.sh

echo "✅ 脚本权限设置完成"

echo ""
echo "可用的配置脚本："
echo "1. ./server01-dns-setup.sh    - DNS服务器配置"
echo "2. ./server02-web1-setup.sh   - Web服务器1 (Apache+MySQL+PHP)"
echo "3. ./server03-web2-setup.sh   - Web服务器2 (Apache+SSL)"
echo ""
echo "使用方法："
echo "sudo ./server01-dns-setup.sh"
echo "sudo ./server02-web1-setup.sh"
echo "sudo ./server03-web2-setup.sh"
echo ""
echo "注意：所有脚本都需要root权限运行"
